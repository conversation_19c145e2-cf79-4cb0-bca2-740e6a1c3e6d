// pages/login/login.js
const auth = require('../../api/auth');
const storage = require('../../utils/storage');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    loginLoading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 检查是否已登录，如果已登录则直接返回
    this.checkLoginStatus();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时的处理
  },

  /**
   * 检查登录状态
   */
  async checkLoginStatus() {
    try {
      const app = getApp();
      
      if (!app) {
        console.error('无法获取应用实例');
        return;
      }

      // 使用全局的登录状态检查方法
      const isLoggedIn = await app.checkLoginStatus({
        showToast: false,
        redirectToLogin: false,
        onSuccess: (userInfo) => {
          // 已登录，跳转到用户页面
          console.log('用户已登录，跳转到用户页面:', userInfo);
          wx.redirectTo({
            url: '/pages/user/userIndex/userIndex'
          });
        },
        onFail: (error) => {
          // 未登录或登录失败，留在登录页面
          console.log('用户未登录，留在登录页面:', error);
        }
      });
    } catch (error) {
      console.log('检查登录状态异常:', error);
    }
  },

  /**
   * 处理微信登录
   */
  async handleWxLogin() {
    if (this.data.loginLoading) return;

    try {
      this.setData({ loginLoading: true });

      // 获取微信登录凭证
      const loginRes = await this.getWxLoginCode();
      
      if (!loginRes.code) {
        throw new Error('获取登录凭证失败');
      }

      // 调用后端登录接口
      const authRes = await auth.wxLogin(loginRes.code);
      
      if (authRes && authRes.success) {
        // 处理token - 后端返回JWT对象格式
        const tokenData = authRes.data.token;
        if (!tokenData || !tokenData.accessToken) {
          throw new Error('无效的token格式');
        }
        
        // 保存token和用户信息 (使用统一的存储工具)
        storage.setToken(tokenData.accessToken);
        
        // 处理用户信息 - 后端返回的是user字段，不是userInfo
        const userInfo = authRes.data.user;
        if (userInfo) {
          storage.setUserInfo(userInfo);
        }
        
        // 更新全局数据状态
        const app = getApp();
        if (app && app.updateUserLoginStatus) {
          // 直接传递已保存的数据，避免重复保存
          app.globalData.userInfo = userInfo;
          app.globalData.isLoggedIn = true;
        }
        
        wx.showToast({
          title: '登录成功',
          icon: 'success',
          duration: 1000
        });
        
        // 确保数据保存完成后再跳转
        setTimeout(() => {
          this.navigateAfterLogin();
        }, 1200);
        
      } else {
        throw new Error(authRes.message || '登录失败');
      }
    } catch (error) {
      console.error('登录失败:', error);
      wx.showToast({
        title: error.message || '登录失败，请重试',
        icon: 'error',
        duration: 2000
      });
    } finally {
      this.setData({ loginLoading: false });
    }
  },

  /**
   * 获取微信登录凭证
   */
  getWxLoginCode() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      });
    });
  },

  /**
   * 登录成功后的导航
   */
  navigateAfterLogin() {
    // 直接跳转到用户页面
    wx.redirectTo({
      url: '/pages/user/userIndex/userIndex',
      fail: (error) => {
        console.error('跳转用户页面失败:', error);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'error'
        });
      }
    });
  },

  /**
   * 显示用户协议
   */
  showUserAgreement() {
    wx.showModal({
      title: '用户协议',
      content: '这里是用户协议的内容...\n\n您可以在设置页面查看完整协议内容。',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  /**
   * 显示隐私政策
   */
  showPrivacyPolicy() {
    wx.showModal({
      title: '隐私政策',
      content: '这里是隐私政策的内容...\n\n您可以在设置页面查看完整隐私政策。',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '言策AI量化投资',
      desc: '智能投资，让财富增值更简单',
      path: '/pages/login/login'
    };
  },

  /**
   * 用户点击右上角分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: '言策AI量化投资 - 智能投资助手',
      query: 'from=timeline'
    };
  }
});
